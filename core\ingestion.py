import os
import logging
from core.extractor import extract_content
from core.chunker import biomedical_chunking
from core.embedder import get_embedding
from core.metadata_store import MetadataStore
from retrieval.vector_store import VectorStore

logger = logging.getLogger(__name__)

class Ingestion:
    def __init__(self):
        os.makedirs("data/vector_index", exist_ok=True)
        os.makedirs("data/metadata", exist_ok=True)
        
        self.vector_store = VectorStore()
        self.metadata_store = MetadataStore()
        
    def ingest_file(self, file_path):
        """Process a single file through the complete ingestion pipeline"""
        try:
            logger.info(f"Processing file: {file_path}")
            filename = os.path.basename(file_path)
            
            # Extract content - returns [(page_num, content), ...]
            extracted_content = extract_content(file_path)
            if not extracted_content:
                logger.warning(f"No content extracted from {file_path}")
                return
            
            # Process each page/section
            all_chunks = []
            for page_num, content in extracted_content:
                if content and content.strip():
                    page_chunks = biomedical_chunking(content, filename, page_num)
                    all_chunks.extend(page_chunks)
            
            # Store chunks
            if all_chunks:
                self._store_chunks(all_chunks)
                logger.info(f"Successfully processed {len(all_chunks)} chunks from {filename}")
        
        except Exception as e:
            logger.error(f"Failed to ingest {file_path}: {str(e)}")
            raise
    
    def _store_chunks(self, chunks):
        """Store chunks in both vector store and metadata store"""
        embeddings = []
        
        for chunk in chunks:
            try:
                embedding = get_embedding(chunk["text"])
                if embedding is not None:
                    embeddings.append(embedding)
                    self.metadata_store.add_chunk(chunk)
                else:
                    logger.warning(f"Skipping chunk due to empty embedding: {chunk['text'][:100]}...")
            except Exception as e:
                logger.error(f"Embedding failed for chunk: {chunk['text'][:100]}... Error: {e}")
        
        if embeddings:
            try:
                self.vector_store.add_vectors(embeddings)
            except Exception as e:
                logger.error(f"Vector store addition failed: {e}")
        
        try:
            self.metadata_store.save()
        except Exception as e:
            logger.error(f"Metadata saving failed: {e}")

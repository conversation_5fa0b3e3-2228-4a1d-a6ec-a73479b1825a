#!/usr/bin/env python3
"""
Setup script for Document RAG System
Ensures all dependencies are properly installed
"""

import subprocess
import sys
import os

def install_requirements():
    """Install all required packages"""
    print("📦 Installing required packages...")
    
    try:
        # Install from requirements.txt
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install packages: {e}")
        return False

def setup_nltk_data():
    """Download required NLTK data"""
    print("📚 Setting up NLTK data...")
    
    try:
        import nltk
        nltk.download('punkt', quiet=True)
        print("✅ NLTK data downloaded successfully!")
        return True
    except Exception as e:
        print(f"❌ Failed to setup NLTK data: {e}")
        return False

def verify_installation():
    """Verify that all key packages can be imported"""
    print("🔍 Verifying installation...")
    
    required_packages = [
        'streamlit',
        'openai', 
        'pymongo',
        'faiss',
        'numpy',
        'pandas',
        'nltk',
        'rank_bm25',
        'fitz',  # PyMuPDF
        'docx',  # python-docx
        'PIL',   # Pillow
        'pytesseract',
        'dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        return False
    else:
        print("\n✅ All packages verified!")
        return True

def check_environment():
    """Check if .env file exists"""
    print("🔧 Checking environment configuration...")
    
    if os.path.exists('.env'):
        print("✅ .env file found")
        return True
    else:
        print("⚠️  .env file not found")
        print("   Please create a .env file with your OpenAI API key:")
        print("   OPENAI_API_KEY=your_api_key_here")
        return False

def main():
    """Main setup function"""
    print("🚀 Document RAG System Setup")
    print("=" * 40)
    
    success = True
    
    # Install requirements
    if not install_requirements():
        success = False
    
    # Setup NLTK data
    if not setup_nltk_data():
        success = False
    
    # Verify installation
    if not verify_installation():
        success = False
    
    # Check environment
    env_ok = check_environment()
    
    print("\n" + "=" * 40)
    
    if success and env_ok:
        print("🎉 Setup completed successfully!")
        print("\n🚀 To start the application:")
        print("   streamlit run app/main.py")
    elif success and not env_ok:
        print("⚠️  Setup completed but .env file is missing")
        print("   Create .env file with OPENAI_API_KEY before running")
    else:
        print("❌ Setup failed. Please check the errors above.")
    
    return success and env_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

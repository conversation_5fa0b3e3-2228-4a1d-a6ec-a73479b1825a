import re
import nltk
import logging
from app.config import SECTION_HEADERS, MAX_CHUNK_TOKENS

logger = logging.getLogger(__name__)

# Ensure NLTK sentence tokenizer is available
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

def create_chunk(text, filename, section, chunk_index):
    """Create a standardized chunk object"""
    return {
        "text": text.strip(),
        "metadata": {
            "file_name": filename,
            "source_file": filename,  # Add for citations
            "section": section,
            "chunk_index": chunk_index,
            "page": "N/A"  # Add page info
        }
    }

def create_chunk_with_page(text, filename, section, chunk_index, page_num):
    """Create a standardized chunk object with page info"""
    return {
        "text": text.strip(),
        "metadata": {
            "file_name": filename,
            "source_file": filename,
            "section": section,
            "chunk_index": chunk_index,
            "page": page_num if page_num is not None else "N/A"
        }
    }

def biomedical_chunking(text, filename, page_num=None, max_tokens=MAX_CHUNK_TOKENS):
    """
    Splits biomedical documents into structured, section-aware chunks.
    """
    if not text.strip():
        return []

    section_pattern = re.compile(r'\b(' + '|'.join(SECTION_HEADERS) + r')\b', re.IGNORECASE)
    current_section = "OTHER"
    chunks = []
    buffer = ""
    token_count = 0

    for sentence in nltk.sent_tokenize(text):
        clean_sent = sentence.strip()
        if not clean_sent:
            continue

        # Check for new section headers
        section_match = section_pattern.search(clean_sent)
        if section_match:
            # Save current buffer if it has content
            if buffer.strip():
                chunks.append(
                    create_chunk_with_page(buffer, filename, current_section, len(chunks), page_num)
                )
                buffer = ""
                token_count = 0
            
            current_section = section_match.group(0).upper()
            continue

        sent_tokens = len(clean_sent.split())

        if token_count + sent_tokens > max_tokens and buffer:
            chunks.append(
                create_chunk_with_page(buffer, filename, current_section, len(chunks), page_num)
            )
            buffer = clean_sent
            token_count = sent_tokens
        else:
            if buffer:
                buffer += " " + clean_sent
            else:
                buffer = clean_sent
            token_count += sent_tokens

    # Add final buffer if it has content
    if buffer.strip():
        chunks.append(
            create_chunk_with_page(buffer, filename, current_section, len(chunks), page_num)
        )

    return chunks

import re
import nltk
import logging
from typing import List, Dict, Tuple
from app.config import SECTION_HEADERS, MAX_CHUNK_TOKENS, CHUNK_OVERLAP

logger = logging.getLogger(__name__)

# Ensure NLTK sentence tokenizer is available
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

def create_chunk(text, filename, section, chunk_index):
    """Create a standardized chunk object"""
    return {
        "text": text.strip(),
        "metadata": {
            "file_name": filename,
            "source_file": filename,  # Add for citations
            "section": section,
            "chunk_index": chunk_index,
            "page": "N/A"  # Add page info
        }
    }

def create_chunk_with_page(text, filename, section, chunk_index, page_num, content_type="text", table_caption=None):
    """Create a standardized chunk object with enhanced metadata"""
    metadata = {
        "file_name": filename,
        "source_file": filename,
        "section": section,
        "chunk_index": chunk_index,
        "page": page_num if page_num is not None else "N/A",
        "content_type": content_type,  # text, table, figure, mixed
        "token_count": len(text.split()),
        "char_count": len(text)
    }

    # Add table-specific metadata
    if table_caption:
        metadata["table_caption"] = table_caption

    # Detect content characteristics
    if "**TABLE" in text or "|" in text:
        metadata["content_type"] = "table"
    elif "**FIGURE" in text:
        metadata["content_type"] = "figure"
    elif any(keyword in text.lower() for keyword in ["table", "figure", "chart", "graph"]):
        metadata["content_type"] = "mixed"

    return {
        "text": text.strip(),
        "metadata": metadata
    }

def biomedical_chunking(text, filename, page_num=None, max_tokens=MAX_CHUNK_TOKENS):
    """
    Enhanced biomedical chunking with overlap, dynamic sizing, and content-aware processing.
    """
    if not text.strip():
        return []

    # First, separate tables and figures from regular text
    content_blocks = separate_content_blocks(text)

    all_chunks = []

    for block in content_blocks:
        if block["type"] == "table":
            # Tables get their own chunks with larger size allowance
            table_chunks = process_table_block(block, filename, page_num, len(all_chunks))
            all_chunks.extend(table_chunks)
        elif block["type"] == "figure":
            # Figures get their own chunks
            figure_chunks = process_figure_block(block, filename, page_num, len(all_chunks))
            all_chunks.extend(figure_chunks)
        else:
            # Regular text processing with overlap
            text_chunks = process_text_block(block, filename, page_num, len(all_chunks), max_tokens)
            all_chunks.extend(text_chunks)

    # Add overlap between adjacent chunks
    overlapped_chunks = add_chunk_overlap(all_chunks)

    return overlapped_chunks

def separate_content_blocks(text: str) -> List[Dict]:
    """Separate text into content blocks (tables, figures, regular text)"""
    blocks = []
    lines = text.split('\n')
    current_block = {"type": "text", "content": [], "section": "OTHER"}

    i = 0
    while i < len(lines):
        line = lines[i].strip()

        if line.startswith("**TABLE"):
            # Save current text block if it has content
            if current_block["content"]:
                blocks.append({
                    "type": current_block["type"],
                    "content": '\n'.join(current_block["content"]),
                    "section": current_block["section"]
                })

            # Process table block
            table_content = [line]
            i += 1
            while i < len(lines) and not lines[i].strip().startswith("**"):
                table_content.append(lines[i])
                i += 1

            blocks.append({
                "type": "table",
                "content": '\n'.join(table_content),
                "section": "TABLE"
            })

            current_block = {"type": "text", "content": [], "section": "OTHER"}
            continue

        elif line.startswith("**FIGURE"):
            # Save current text block if it has content
            if current_block["content"]:
                blocks.append({
                    "type": current_block["type"],
                    "content": '\n'.join(current_block["content"]),
                    "section": current_block["section"]
                })

            # Process figure block
            figure_content = [line]
            i += 1
            while i < len(lines) and not lines[i].strip().startswith("**"):
                figure_content.append(lines[i])
                i += 1

            blocks.append({
                "type": "figure",
                "content": '\n'.join(figure_content),
                "section": "FIGURE"
            })

            current_block = {"type": "text", "content": [], "section": "OTHER"}
            continue

        else:
            # Check for section headers
            section_pattern = re.compile(r'\b(' + '|'.join(SECTION_HEADERS) + r')\b', re.IGNORECASE)
            section_match = section_pattern.search(line)
            if section_match:
                current_block["section"] = section_match.group(0).upper()

            current_block["content"].append(lines[i])

        i += 1

    # Add final text block
    if current_block["content"]:
        blocks.append({
            "type": current_block["type"],
            "content": '\n'.join(current_block["content"]),
            "section": current_block["section"]
        })

    return blocks

def process_table_block(block: Dict, filename: str, page_num, chunk_start_idx: int) -> List[Dict]:
    """Process table blocks with appropriate sizing"""
    content = block["content"]

    # Tables can be larger than regular text chunks
    table_max_tokens = MAX_CHUNK_TOKENS * 2

    if len(content.split()) <= table_max_tokens:
        # Single table chunk
        return [create_chunk_with_page(
            content, filename, block["section"],
            chunk_start_idx, page_num, content_type="table"
        )]
    else:
        # Split large tables by rows
        lines = content.split('\n')
        chunks = []
        current_chunk = []
        current_tokens = 0

        for line in lines:
            line_tokens = len(line.split())
            if current_tokens + line_tokens > table_max_tokens and current_chunk:
                chunk_content = '\n'.join(current_chunk)
                chunks.append(create_chunk_with_page(
                    chunk_content, filename, block["section"],
                    chunk_start_idx + len(chunks), page_num, content_type="table"
                ))
                current_chunk = [line]
                current_tokens = line_tokens
            else:
                current_chunk.append(line)
                current_tokens += line_tokens

        # Add final chunk
        if current_chunk:
            chunk_content = '\n'.join(current_chunk)
            chunks.append(create_chunk_with_page(
                chunk_content, filename, block["section"],
                chunk_start_idx + len(chunks), page_num, content_type="table"
            ))

        return chunks

def process_figure_block(block: Dict, filename: str, page_num, chunk_start_idx: int) -> List[Dict]:
    """Process figure blocks"""
    return [create_chunk_with_page(
        block["content"], filename, block["section"],
        chunk_start_idx, page_num, content_type="figure"
    )]

def process_text_block(block: Dict, filename: str, page_num, chunk_start_idx: int, max_tokens: int) -> List[Dict]:
    """Process regular text blocks with sentence-aware chunking"""
    content = block["content"]
    if not content.strip():
        return []

    chunks = []
    sentences = nltk.sent_tokenize(content)

    current_chunk = []
    current_tokens = 0

    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue

        sent_tokens = len(sentence.split())

        if current_tokens + sent_tokens > max_tokens and current_chunk:
            # Create chunk from current buffer
            chunk_content = ' '.join(current_chunk)
            chunks.append(create_chunk_with_page(
                chunk_content, filename, block["section"],
                chunk_start_idx + len(chunks), page_num, content_type="text"
            ))
            current_chunk = [sentence]
            current_tokens = sent_tokens
        else:
            current_chunk.append(sentence)
            current_tokens += sent_tokens

    # Add final chunk
    if current_chunk:
        chunk_content = ' '.join(current_chunk)
        chunks.append(create_chunk_with_page(
            chunk_content, filename, block["section"],
            chunk_start_idx + len(chunks), page_num, content_type="text"
        ))

    return chunks

def add_chunk_overlap(chunks: List[Dict]) -> List[Dict]:
    """Add overlap between adjacent text chunks"""
    if len(chunks) <= 1:
        return chunks

    overlapped_chunks = []

    for i, chunk in enumerate(chunks):
        current_text = chunk["text"]

        # Skip overlap for tables and figures
        if chunk["metadata"]["content_type"] in ["table", "figure"]:
            overlapped_chunks.append(chunk)
            continue

        # Add overlap from previous chunk
        if i > 0:
            prev_chunk = chunks[i-1]
            if prev_chunk["metadata"]["content_type"] == "text":
                prev_words = prev_chunk["text"].split()
                if len(prev_words) > CHUNK_OVERLAP:
                    overlap_text = " ".join(prev_words[-CHUNK_OVERLAP:])
                    current_text = overlap_text + " " + current_text

        # Add overlap from next chunk
        if i < len(chunks) - 1:
            next_chunk = chunks[i+1]
            if next_chunk["metadata"]["content_type"] == "text":
                next_words = next_chunk["text"].split()
                if len(next_words) > CHUNK_OVERLAP:
                    overlap_text = " ".join(next_words[:CHUNK_OVERLAP])
                    current_text = current_text + " " + overlap_text

        # Update chunk with overlapped text
        overlapped_chunk = chunk.copy()
        overlapped_chunk["text"] = current_text
        overlapped_chunk["metadata"] = chunk["metadata"].copy()
        overlapped_chunk["metadata"]["token_count"] = len(current_text.split())
        overlapped_chunk["metadata"]["char_count"] = len(current_text)
        overlapped_chunk["metadata"]["has_overlap"] = True

        overlapped_chunks.append(overlapped_chunk)

    return overlapped_chunks

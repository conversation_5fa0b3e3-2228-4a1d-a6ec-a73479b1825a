import openai
from typing import List, Dict
from app.config import OPENAI_MODEL
from llm.prompts import BiomedicalPrompts

def generate_cited_response(query: str, context_chunks: List[Dict]) -> str:
    """
    Generate a grounded biomedical answer using only the uploaded content.
    Strictly avoids hallucination and cites source file/page.
    """
    if not context_chunks:
        return "I couldn't find relevant information in the uploaded files. Please rephrase your question or upload more relevant documents."

    # Construct a grounded prompt using structured template
    prompt = BiomedicalPrompts.answer_prompt(query, context_chunks)

    try:
        response = openai.chat.completions.create(
            model=OPENAI_MODEL,
            messages=[
                {"role": "system", "content": prompt["system"]},
                {"role": "user", "content": prompt["user"]}
            ],
            temperature=0,
            max_tokens=900
        )
        return response.choices[0].message.content.strip()

    except Exception as e:
        return f"Error generating response: {str(e)}"

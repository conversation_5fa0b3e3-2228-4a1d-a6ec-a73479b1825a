import openai
from typing import List, Dict, Tuple
from app.config import OPENAI_MODEL, MAX_CONTEXT_TOKENS
from llm.prompts import BiomedicalPrompts
import logging

logger = logging.getLogger(__name__)

def generate_cited_response(query: str, context_chunks: List[Dict]) -> str:
    """
    Generate a grounded biomedical answer using expanded context window.
    Maximizes context usage up to gpt-4o-mini token limit.
    """
    if not context_chunks:
        return "I couldn't find relevant information in the uploaded files. Please rephrase your question or upload more relevant documents."

    # Optimize context chunks to fit within token limit
    optimized_chunks = optimize_context_for_token_limit(context_chunks, query)

    if not optimized_chunks:
        return "The retrieved content is too large to process. Please try a more specific query."

    # Construct enhanced prompt with expanded context
    prompt = BiomedicalPrompts.answer_prompt(query, optimized_chunks)

    try:
        response = openai.chat.completions.create(
            model=OPENAI_MODEL,
            messages=[
                {"role": "system", "content": prompt["system"]},
                {"role": "user", "content": prompt["user"]}
            ],
            temperature=0,
            max_tokens=1200  # Increased for more detailed responses
        )

        answer = response.choices[0].message.content.strip()

        # Add context summary for transparency
        context_summary = generate_context_summary(optimized_chunks)

        return f"{answer}\n\n---\n**Context Used:** {context_summary}"

    except Exception as e:
        logger.error(f"Response generation failed: {e}")
        return f"Error generating response: {str(e)}"

def optimize_context_for_token_limit(chunks: List[Dict], query: str) -> List[Dict]:
    """
    Optimize context chunks to maximize information within token limit.
    Prioritizes tables, figures, and high-relevance content.
    """
    if not chunks:
        return []

    # Estimate tokens for query and system prompt (roughly 500 tokens)
    reserved_tokens = 800  # Buffer for prompt overhead and response
    available_tokens = MAX_CONTEXT_TOKENS - reserved_tokens

    # Prioritize chunks by content type and relevance
    prioritized_chunks = prioritize_chunks_for_context(chunks, query)

    # Select chunks within token limit
    selected_chunks = []
    current_tokens = 0

    for chunk in prioritized_chunks:
        chunk_tokens = estimate_tokens(chunk["text"])

        if current_tokens + chunk_tokens <= available_tokens:
            selected_chunks.append(chunk)
            current_tokens += chunk_tokens
        else:
            # Try to fit a truncated version of high-priority chunks
            if chunk.get("metadata", {}).get("content_type") in ["table", "figure"]:
                truncated_text = truncate_text_to_tokens(chunk["text"], available_tokens - current_tokens)
                if truncated_text and len(truncated_text.split()) > 50:  # Minimum meaningful size
                    truncated_chunk = chunk.copy()
                    truncated_chunk["text"] = truncated_text
                    selected_chunks.append(truncated_chunk)
            break

    return selected_chunks

def prioritize_chunks_for_context(chunks: List[Dict], query: str) -> List[Dict]:
    """Prioritize chunks for context inclusion based on content type and relevance"""
    query_lower = query.lower()

    # Categorize chunks
    table_chunks = []
    figure_chunks = []
    text_chunks = []

    for chunk in chunks:
        content_type = chunk.get("metadata", {}).get("content_type", "text")
        if content_type == "table":
            table_chunks.append(chunk)
        elif content_type == "figure":
            figure_chunks.append(chunk)
        else:
            text_chunks.append(chunk)

    # Prioritize based on query intent
    prioritized = []

    # If query mentions tables/data, prioritize tables
    if any(keyword in query_lower for keyword in ["table", "data", "results", "values", "statistics"]):
        prioritized.extend(table_chunks)
        prioritized.extend(figure_chunks)
        prioritized.extend(text_chunks)
    # If query mentions figures/images, prioritize figures
    elif any(keyword in query_lower for keyword in ["figure", "image", "diagram", "chart", "graph"]):
        prioritized.extend(figure_chunks)
        prioritized.extend(table_chunks)
        prioritized.extend(text_chunks)
    # Default: balanced approach
    else:
        # Interleave high-value content
        max_len = max(len(table_chunks), len(figure_chunks), len(text_chunks))
        for i in range(max_len):
            if i < len(table_chunks):
                prioritized.append(table_chunks[i])
            if i < len(figure_chunks):
                prioritized.append(figure_chunks[i])
            if i < len(text_chunks):
                prioritized.append(text_chunks[i])

    return prioritized

def estimate_tokens(text: str) -> int:
    """Rough token estimation (1 token ≈ 0.75 words for English)"""
    return int(len(text.split()) * 1.33)

def truncate_text_to_tokens(text: str, max_tokens: int) -> str:
    """Truncate text to fit within token limit"""
    words = text.split()
    max_words = int(max_tokens * 0.75)  # Conservative estimate

    if len(words) <= max_words:
        return text

    # For tables, try to keep complete rows
    if "|" in text and "**TABLE" in text:
        lines = text.split('\n')
        truncated_lines = []
        current_tokens = 0

        for line in lines:
            line_tokens = estimate_tokens(line)
            if current_tokens + line_tokens <= max_tokens:
                truncated_lines.append(line)
                current_tokens += line_tokens
            else:
                break

        if len(truncated_lines) > 3:  # Keep at least header + separator + one data row
            return '\n'.join(truncated_lines) + "\n[... table truncated ...]"

    # Default word-based truncation
    truncated_words = words[:max_words]
    return ' '.join(truncated_words) + " [... truncated ...]"

def generate_context_summary(chunks: List[Dict]) -> str:
    """Generate a summary of the context used"""
    if not chunks:
        return "No context used"

    summary_parts = []

    # Count by content type
    content_types = {}
    sources = set()

    for chunk in chunks:
        metadata = chunk.get("metadata", {})
        content_type = metadata.get("content_type", "text")
        source_file = metadata.get("source_file", "Unknown")

        content_types[content_type] = content_types.get(content_type, 0) + 1
        sources.add(source_file)

    # Build summary
    type_summary = []
    for content_type, count in content_types.items():
        type_summary.append(f"{count} {content_type} chunk{'s' if count > 1 else ''}")

    summary_parts.append(", ".join(type_summary))
    summary_parts.append(f"from {len(sources)} source{'s' if len(sources) > 1 else ''}")

    return " ".join(summary_parts)

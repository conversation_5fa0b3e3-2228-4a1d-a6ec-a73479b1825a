import pickle
import os
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class MetadataStore:
    def __init__(self):
        self.chunks = []
        self._load_existing()
        
    def _load_existing(self):
        """Load existing metadata if available"""
        metadata_path = "data/metadata/chunks.pkl"
        if os.path.exists(metadata_path):
            try:
                with open(metadata_path, 'rb') as f:
                    self.chunks = pickle.load(f)
                logger.info(f"Loaded {len(self.chunks)} chunks from existing metadata.")
            except Exception as e:
                logger.warning(f"Failed to load existing metadata: {e}")
                self.chunks = []
        
    def add_chunk(self, chunk_data: Dict[str, Any]):
        """Add chunk to store"""
        self.chunks.append(chunk_data)
    
    def get_all_chunks(self) -> List[Dict[str, Any]]:
        """Get all stored chunks"""
        return self.chunks
    
    def get_chunks_by_file(self, file_name: str) -> List[Dict[str, Any]]:
        """Get chunks from specific file"""
        return [chunk for chunk in self.chunks if chunk["metadata"]["file_name"] == file_name]
    
    def save(self):
        """Save chunks to disk"""
        metadata_path = os.path.join("data", "metadata", "chunks.pkl")
        try:
            os.makedirs(os.path.dirname(metadata_path), exist_ok=True)
            with open(metadata_path, "wb") as f:
                pickle.dump(self.chunks, f)
            logger.info(f"Saved {len(self.chunks)} chunks to {metadata_path}")
        except Exception as e:
            logger.error(f"Failed to save metadata: {e}")
            raise

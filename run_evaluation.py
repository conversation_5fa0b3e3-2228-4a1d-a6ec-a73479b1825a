#!/usr/bin/env python3
"""
Quick evaluation runner for LIFESAVER-AI RAG system
Usage: python run_evaluation.py [test_documents_path]
"""

import sys
import os
import logging
from pathlib import Path

# Add project root to path
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

from evaluation.test_suite import run_evaluation_suite

def main():
    """Main evaluation runner"""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Get test documents path from command line or use default
    if len(sys.argv) > 1:
        test_docs_path = sys.argv[1]
    else:
        test_docs_path = "evaluation/test_documents"
    
    print(f"🔬 LIFESAVER-AI RAG System Evaluation")
    print(f"📁 Test documents path: {test_docs_path}")
    print("=" * 50)
    
    # Check if test documents exist
    if not os.path.exists(test_docs_path):
        print(f"❌ Test documents path not found: {test_docs_path}")
        print("\n📝 To run evaluation:")
        print("1. Create the test_documents directory")
        print("2. Add sample PDF/DOCX files with tables and figures")
        print("3. Run: python run_evaluation.py")
        return
    
    # Run evaluation
    try:
        results = run_evaluation_suite(test_docs_path)
        
        if results:
            print("\n✅ Evaluation completed successfully!")
            print(f"📊 Results saved to: evaluation/results/")
            
            # Quick summary
            summary = results.get("summary", {})
            print(f"\n📈 Quick Summary:")
            print(f"   Success Rate: {summary.get('success_rate', 0):.1%}")
            print(f"   Content Coverage: {summary.get('avg_content_type_coverage', 0):.1%}")
            print(f"   Citation Rate: {summary.get('citation_rate', 0):.1%}")
        else:
            print("❌ Evaluation failed!")
            
    except Exception as e:
        print(f"❌ Evaluation error: {e}")
        logging.error(f"Evaluation failed: {e}", exc_info=True)

if __name__ == "__main__":
    main()

from typing import List, Dict

def batch_rerank(query: str, chunks: List[Dict], top_k: int = 5) -> List[Dict]:
    """Rerank retrieved chunks for better relevance"""
    
    if not chunks:
        return []
    
    # Simple relevance scoring based on query terms
    scored_chunks = []
    query_terms = query.lower().split()
    
    for chunk in chunks:
        text = chunk["text"].lower()
        
        # Calculate relevance score
        score = 0
        for term in query_terms:
            score += text.count(term)
        
        # Boost score for biomedical terms
        biomedical_terms = ["car-t", "therapy", "treatment", "clinical", "patient", "cell"]
        for term in biomedical_terms:
            if term in text:
                score += 2
        
        scored_chunks.append({
            "passage": chunk,
            "score": score
        })
    
    # Sort by score and return top_k
    scored_chunks.sort(key=lambda x: x["score"], reverse=True)
    return scored_chunks[:top_k]

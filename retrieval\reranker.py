import openai
import logging
from typing import List, Dict, Tuple
from app.config import OPENAI_MODEL, SECTION_WEIGHTS
from llm.prompts import BiomedicalPrompts

logger = logging.getLogger(__name__)

def batch_rerank(query: str, chunks: List[Dict], top_k: int = 5) -> List[Dict]:
    """Enhanced LLM-based reranking with cross-encoder approach"""

    if not chunks:
        return []

    if len(chunks) <= top_k:
        # If we have fewer chunks than requested, return all with scores
        return [{"passage": chunk, "score": 1.0} for chunk in chunks]

    try:
        # Use LLM-based cross-encoder reranking
        scored_chunks = llm_cross_encoder_rerank(query, chunks)

        # Apply section-based boosting
        boosted_chunks = apply_section_boosting(scored_chunks)

        # Apply content-type boosting
        final_chunks = apply_content_type_boosting(query, boosted_chunks)

        # Sort by final score and return top_k
        final_chunks.sort(key=lambda x: x["score"], reverse=True)
        return final_chunks[:top_k]

    except Exception as e:
        logger.error(f"LLM reranking failed, falling back to simple reranking: {e}")
        return simple_fallback_rerank(query, chunks, top_k)

def llm_cross_encoder_rerank(query: str, chunks: List[Dict]) -> List[Dict]:
    """Use LLM as cross-encoder to score query-passage relevance"""
    scored_chunks = []

    # Process chunks in batches to avoid token limits
    batch_size = 5
    for i in range(0, len(chunks), batch_size):
        batch = chunks[i:i+batch_size]

        try:
            # Create reranking prompt
            passages_for_prompt = [{"text": chunk["text"][:1000]} for chunk in batch]  # Truncate for token limits
            prompt = BiomedicalPrompts.rerank_prompt(query, passages_for_prompt)

            response = openai.chat.completions.create(
                model=OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": prompt["system"]},
                    {"role": "user", "content": prompt["user"]}
                ],
                temperature=0,
                max_tokens=100
            )

            # Parse scores from response
            scores_text = response.choices[0].message.content.strip()
            scores = parse_rerank_scores(scores_text, len(batch))

            # Combine chunks with scores
            for j, chunk in enumerate(batch):
                score = scores[j] if j < len(scores) else 0.5
                scored_chunks.append({
                    "passage": chunk,
                    "score": score
                })

        except Exception as e:
            logger.warning(f"Batch reranking failed for batch {i//batch_size + 1}: {e}")
            # Fallback to neutral scores for this batch
            for chunk in batch:
                scored_chunks.append({
                    "passage": chunk,
                    "score": 0.5
                })

    return scored_chunks

def parse_rerank_scores(scores_text: str, expected_count: int) -> List[float]:
    """Parse LLM reranking scores from response"""
    try:
        # Expected format: "0.8|0.6|0.9|0.3|0.7"
        if "|" in scores_text:
            score_strings = scores_text.split("|")
        else:
            # Fallback: try to extract numbers
            import re
            score_strings = re.findall(r'\d+\.?\d*', scores_text)

        scores = []
        for score_str in score_strings:
            try:
                score = float(score_str.strip())
                # Clamp score between 0 and 1
                score = max(0.0, min(1.0, score))
                scores.append(score)
            except ValueError:
                scores.append(0.5)  # Default score

        # Pad or truncate to expected count
        while len(scores) < expected_count:
            scores.append(0.5)

        return scores[:expected_count]

    except Exception as e:
        logger.warning(f"Score parsing failed: {e}")
        return [0.5] * expected_count

def apply_section_boosting(scored_chunks: List[Dict]) -> List[Dict]:
    """Apply section-based score boosting"""
    boosted_chunks = []

    for item in scored_chunks:
        chunk = item["passage"]
        base_score = item["score"]

        # Get section weight
        section = chunk.get("metadata", {}).get("section", "OTHER")
        section_weight = SECTION_WEIGHTS.get(section, 1.0)

        # Apply boosting
        boosted_score = base_score * section_weight

        boosted_chunks.append({
            "passage": chunk,
            "score": min(1.0, boosted_score)  # Cap at 1.0
        })

    return boosted_chunks

def apply_content_type_boosting(query: str, scored_chunks: List[Dict]) -> List[Dict]:
    """Apply content-type specific boosting based on query"""
    query_lower = query.lower()

    # Detect query intent
    table_keywords = ["table", "data", "results", "values", "numbers", "statistics"]
    figure_keywords = ["figure", "image", "diagram", "chart", "graph", "visualization"]

    is_table_query = any(keyword in query_lower for keyword in table_keywords)
    is_figure_query = any(keyword in query_lower for keyword in figure_keywords)

    boosted_chunks = []

    for item in scored_chunks:
        chunk = item["passage"]
        base_score = item["score"]

        content_type = chunk.get("metadata", {}).get("content_type", "text")

        # Apply content-type boosting
        boost_factor = 1.0
        if is_table_query and content_type == "table":
            boost_factor = 1.5
        elif is_figure_query and content_type == "figure":
            boost_factor = 1.4
        elif content_type in ["table", "figure"] and not (is_table_query or is_figure_query):
            boost_factor = 1.2  # Slight boost for structured content

        boosted_score = base_score * boost_factor

        boosted_chunks.append({
            "passage": chunk,
            "score": min(1.0, boosted_score)  # Cap at 1.0
        })

    return boosted_chunks

def simple_fallback_rerank(query: str, chunks: List[Dict], top_k: int) -> List[Dict]:
    """Fallback to simple keyword-based reranking"""
    scored_chunks = []
    query_terms = query.lower().split()

    for chunk in chunks:
        text = chunk["text"].lower()

        # Calculate relevance score
        score = 0
        for term in query_terms:
            score += text.count(term)

        # Boost score for biomedical terms
        biomedical_terms = ["car-t", "therapy", "treatment", "clinical", "patient", "cell", "table", "figure"]
        for term in biomedical_terms:
            if term in text:
                score += 2

        # Normalize score
        max_possible_score = len(query_terms) * 10 + len(biomedical_terms) * 2
        normalized_score = min(1.0, score / max_possible_score) if max_possible_score > 0 else 0.5

        scored_chunks.append({
            "passage": chunk,
            "score": normalized_score
        })

    # Sort by score and return top_k
    scored_chunks.sort(key=lambda x: x["score"], reverse=True)
    return scored_chunks[:top_k]

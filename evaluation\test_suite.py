"""
Automated Evaluation Suite for LIFESAVER-AI RAG System
Tests retrieval accuracy, answer quality, and table processing capabilities.
"""

import json
import logging
import os
from typing import List, Dict, Any, Tuple
from datetime import datetime
import pandas as pd

from core.ingestion import Ingestion
from retrieval.hybrid_search import biomedical_retrieval
from retrieval.reranker import batch_rerank
from llm.response_generator import generate_cited_response

logger = logging.getLogger(__name__)

class RAGEvaluator:
    """Comprehensive evaluation suite for RAG system performance"""
    
    def __init__(self):
        self.test_queries = self._load_test_queries()
        self.ingestion_system = None
        self.results = []
    
    def _load_test_queries(self) -> List[Dict]:
        """Load test queries with expected answers and metadata"""
        return [
            {
                "id": "table_data_1",
                "query": "What are the efficacy rates shown in the clinical trial results table?",
                "expected_content_types": ["table"],
                "expected_keywords": ["efficacy", "rate", "clinical", "trial"],
                "difficulty": "medium",
                "category": "table_extraction"
            },
            {
                "id": "table_data_2", 
                "query": "Show me the patient demographics data from the study tables",
                "expected_content_types": ["table"],
                "expected_keywords": ["patient", "demographics", "age", "gender"],
                "difficulty": "medium",
                "category": "table_extraction"
            },
            {
                "id": "figure_analysis_1",
                "query": "What does Figure 1 show about CAR-T cell expansion?",
                "expected_content_types": ["figure"],
                "expected_keywords": ["figure", "car-t", "expansion", "cell"],
                "difficulty": "hard",
                "category": "figure_analysis"
            },
            {
                "id": "synthesis_1",
                "query": "Compare the results across different treatment groups",
                "expected_content_types": ["table", "text"],
                "expected_keywords": ["treatment", "group", "compare", "results"],
                "difficulty": "hard",
                "category": "multi_source_synthesis"
            },
            {
                "id": "methods_1",
                "query": "What methodology was used for CAR-T cell manufacturing?",
                "expected_content_types": ["text"],
                "expected_keywords": ["methodology", "manufacturing", "car-t"],
                "difficulty": "easy",
                "category": "text_retrieval"
            },
            {
                "id": "quantitative_1",
                "query": "What are the specific survival rates and confidence intervals?",
                "expected_content_types": ["table", "text"],
                "expected_keywords": ["survival", "rate", "confidence", "interval"],
                "difficulty": "medium",
                "category": "quantitative_data"
            }
        ]
    
    def setup_test_environment(self, test_documents_path: str):
        """Setup test environment with sample documents"""
        try:
            self.ingestion_system = Ingestion()
            
            # Ingest test documents
            if os.path.exists(test_documents_path):
                for filename in os.listdir(test_documents_path):
                    if filename.endswith(('.pdf', '.docx', '.txt')):
                        file_path = os.path.join(test_documents_path, filename)
                        logger.info(f"Ingesting test document: {filename}")
                        self.ingestion_system.ingest_file(file_path)
            else:
                logger.warning(f"Test documents path not found: {test_documents_path}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Test environment setup failed: {e}")
            return False
    
    def run_evaluation(self) -> Dict[str, Any]:
        """Run complete evaluation suite"""
        if not self.ingestion_system:
            raise ValueError("Test environment not setup. Call setup_test_environment() first.")
        
        logger.info("Starting RAG system evaluation...")
        start_time = datetime.now()
        
        results = {
            "timestamp": start_time.isoformat(),
            "test_results": [],
            "summary": {},
            "performance_metrics": {}
        }
        
        chunk_data = self.ingestion_system.metadata_store.get_all_chunks()
        
        for query_test in self.test_queries:
            logger.info(f"Testing query: {query_test['id']}")
            
            test_result = self._evaluate_single_query(
                query_test, 
                chunk_data
            )
            
            results["test_results"].append(test_result)
        
        # Calculate summary metrics
        results["summary"] = self._calculate_summary_metrics(results["test_results"])
        results["performance_metrics"]["total_time"] = (datetime.now() - start_time).total_seconds()
        
        # Save results
        self._save_results(results)
        
        return results
    
    def _evaluate_single_query(self, query_test: Dict, chunk_data: List[Dict]) -> Dict:
        """Evaluate a single query against the RAG system"""
        query = query_test["query"]
        
        try:
            # Retrieval phase
            retrieved_chunks = biomedical_retrieval(
                query=query,
                vector_store=self.ingestion_system.vector_store,
                chunk_data=chunk_data,
                k=20
            )
            
            # Reranking phase
            reranked_chunks = batch_rerank(query, retrieved_chunks, top_k=10)
            best_chunks = [x["passage"] for x in reranked_chunks]
            
            # Response generation phase
            response = generate_cited_response(query, best_chunks)
            
            # Evaluation metrics
            evaluation = {
                "query_id": query_test["id"],
                "query": query,
                "category": query_test["category"],
                "difficulty": query_test["difficulty"],
                "retrieved_count": len(retrieved_chunks),
                "reranked_count": len(best_chunks),
                "response_length": len(response),
                "response": response,
                "metrics": self._calculate_query_metrics(query_test, best_chunks, response)
            }
            
            return evaluation
            
        except Exception as e:
            logger.error(f"Query evaluation failed for {query_test['id']}: {e}")
            return {
                "query_id": query_test["id"],
                "query": query,
                "error": str(e),
                "metrics": {"error": True}
            }
    
    def _calculate_query_metrics(self, query_test: Dict, chunks: List[Dict], response: str) -> Dict:
        """Calculate metrics for a single query evaluation"""
        metrics = {}
        
        # Content type coverage
        expected_types = set(query_test["expected_content_types"])
        retrieved_types = set()
        
        for chunk in chunks:
            content_type = chunk.get("metadata", {}).get("content_type", "text")
            retrieved_types.add(content_type)
        
        metrics["content_type_coverage"] = len(expected_types.intersection(retrieved_types)) / len(expected_types)
        
        # Keyword presence in response
        expected_keywords = query_test["expected_keywords"]
        response_lower = response.lower()
        
        keyword_hits = sum(1 for keyword in expected_keywords if keyword in response_lower)
        metrics["keyword_coverage"] = keyword_hits / len(expected_keywords)
        
        # Citation analysis
        citation_count = response.count("[^")
        metrics["citation_count"] = citation_count
        metrics["has_citations"] = citation_count > 0
        
        # Response quality indicators
        metrics["response_has_numbers"] = any(char.isdigit() for char in response)
        metrics["response_has_table_ref"] = "table" in response_lower
        metrics["response_has_figure_ref"] = "figure" in response_lower
        
        # Table-specific metrics
        if "table" in query_test["category"]:
            table_chunks = [c for c in chunks if c.get("metadata", {}).get("content_type") == "table"]
            metrics["table_chunks_retrieved"] = len(table_chunks)
            metrics["table_content_in_response"] = "|" in response or "table" in response_lower
        
        return metrics
    
    def _calculate_summary_metrics(self, test_results: List[Dict]) -> Dict:
        """Calculate overall summary metrics"""
        if not test_results:
            return {}
        
        valid_results = [r for r in test_results if "error" not in r.get("metrics", {})]
        
        if not valid_results:
            return {"error": "No valid test results"}
        
        # Average metrics
        avg_content_coverage = sum(r["metrics"].get("content_type_coverage", 0) for r in valid_results) / len(valid_results)
        avg_keyword_coverage = sum(r["metrics"].get("keyword_coverage", 0) for r in valid_results) / len(valid_results)
        
        # Citation metrics
        responses_with_citations = sum(1 for r in valid_results if r["metrics"].get("has_citations", False))
        citation_rate = responses_with_citations / len(valid_results)
        
        # Category performance
        category_performance = {}
        for result in valid_results:
            category = result["category"]
            if category not in category_performance:
                category_performance[category] = []
            category_performance[category].append(result["metrics"].get("content_type_coverage", 0))
        
        for category in category_performance:
            scores = category_performance[category]
            category_performance[category] = sum(scores) / len(scores)
        
        return {
            "total_tests": len(test_results),
            "successful_tests": len(valid_results),
            "success_rate": len(valid_results) / len(test_results),
            "avg_content_type_coverage": avg_content_coverage,
            "avg_keyword_coverage": avg_keyword_coverage,
            "citation_rate": citation_rate,
            "category_performance": category_performance
        }
    
    def _save_results(self, results: Dict):
        """Save evaluation results to file"""
        os.makedirs("evaluation/results", exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"evaluation/results/rag_evaluation_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"Evaluation results saved to: {filename}")
    
    def generate_report(self, results: Dict) -> str:
        """Generate human-readable evaluation report"""
        summary = results.get("summary", {})
        
        report = f"""
# RAG System Evaluation Report
**Timestamp:** {results.get('timestamp', 'Unknown')}
**Total Tests:** {summary.get('total_tests', 0)}
**Success Rate:** {summary.get('success_rate', 0):.2%}

## Overall Performance
- **Content Type Coverage:** {summary.get('avg_content_type_coverage', 0):.2%}
- **Keyword Coverage:** {summary.get('avg_keyword_coverage', 0):.2%}
- **Citation Rate:** {summary.get('citation_rate', 0):.2%}

## Category Performance
"""
        
        for category, score in summary.get('category_performance', {}).items():
            report += f"- **{category.replace('_', ' ').title()}:** {score:.2%}\n"
        
        report += "\n## Test Details\n"
        
        for test in results.get('test_results', []):
            if "error" not in test.get('metrics', {}):
                metrics = test['metrics']
                report += f"\n### {test['query_id']} ({test['category']})\n"
                report += f"**Query:** {test['query']}\n"
                report += f"**Content Coverage:** {metrics.get('content_type_coverage', 0):.2%}\n"
                report += f"**Keyword Coverage:** {metrics.get('keyword_coverage', 0):.2%}\n"
                report += f"**Citations:** {metrics.get('citation_count', 0)}\n"
        
        return report

def run_evaluation_suite(test_documents_path: str = "evaluation/test_documents"):
    """Convenience function to run the complete evaluation suite"""
    evaluator = RAGEvaluator()
    
    if not evaluator.setup_test_environment(test_documents_path):
        logger.error("Failed to setup test environment")
        return None
    
    results = evaluator.run_evaluation()
    report = evaluator.generate_report(results)
    
    print(report)
    return results

if __name__ == "__main__":
    # Run evaluation if called directly
    run_evaluation_suite()

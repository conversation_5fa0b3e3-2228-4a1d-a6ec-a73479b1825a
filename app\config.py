# Centralized configuration
OPENAI_MODEL = "gpt-4o-mini"
EMBEDDING_MODEL = "text-embedding-ada-002"
MAX_CHUNK_TOKENS = 384
CHUNK_OVERLAP = 50  # Token overlap between chunks
MAX_CONTEXT_TOKENS = 14000  # Context window for gpt-4o-mini (16k - buffer)

# Biomedical section handling
SECTION_HEADERS = [
    "ABSTRACT", "INTRODUCTION", "METHODS", "METHOD",
    "RESULTS", "DISCUSSION", "CONCLUSION", "REFERENCES",
    "TABLE", "FIGURE", "SUPPLEMENTARY"
]

SECTION_WEIGHTS = {
    "METHODS": 1.8, "METHOD": 1.8, "RESULTS": 1.6,
    "TABLE": 1.7, "FIGURE": 1.5,  # Boost tables and figures
    "CONCLUSION": 1.4, "DISCUSSION": 1.3,
    "INTRODUCTION": 1.2, "ABSTRACT": 1.1,
    "SUPPLEMENTARY": 1.1, "OTHER": 1.0
}

# Table extraction settings
TABLE_EXTRACTION_SETTINGS = {
    "vertical_strategy": "lines",
    "horizontal_strategy": "lines",
    "snap_tolerance": 3,
    "join_tolerance": 3,
    "edge_min_length": 3,
    "min_words_vertical": 3,
    "min_words_horizontal": 1
}

IMAGE_ANALYSIS_PROMPT = (
    "Describe this image in detail, focusing on any visible data, labels, "
    "text, charts, graphs, or structural elements. Include any numerical "
    "values, measurements, or quantitative information present."
)

# Text cleaning patterns
TEXT_CLEANING_PATTERNS = {
    "multiple_spaces": r'\s+',
    "multiple_newlines": r'\n\s*\n\s*\n+',
    "bullet_points": r'^[\s]*[•·▪▫◦‣⁃]\s*',
    "page_numbers": r'^\s*\d+\s*$',
    "headers_footers": r'^[A-Z\s]{3,50}$'
}
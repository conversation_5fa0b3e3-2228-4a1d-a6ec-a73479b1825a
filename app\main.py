import os
import sys
import streamlit as st
from dotenv import load_dotenv

# Setup paths
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
load_dotenv()

# UI modules
from ui.uploader import upload_files
from ui.chatbox import query_input
from ui.preview import show_uploaded_files

# Core pipeline
from core.ingestion import Ingestion
from retrieval.hybrid_search import biomedical_retrieval
from retrieval.reranker import batch_rerank
from llm.response_generator import generate_cited_response

st.set_page_config(page_title="LIFESAVER-AI", layout="wide")
st.title("🔬 LIFESAVER-AI")

# Initialize session state
if 'ingestion_system' not in st.session_state:
    st.session_state.ingestion_system = Ingestion()
if 'ingestion_complete' not in st.session_state:
    st.session_state.ingestion_complete = False

# Upload files
with st.expander("📤 Upload Files", expanded=True):
    files = upload_files()

# Ingest documents
if st.button("🚀 Ingest Files") and files:
    with st.spinner("Extracting, chunking, embedding..."):
        try:
            ingest = st.session_state.ingestion_system
            
            # Process each file
            for file in files:
                file_path = f"data/uploads/{file.name}"
                os.makedirs("data/uploads", exist_ok=True)
                with open(file_path, "wb") as f:
                    f.write(file.getbuffer())
                
                ingest.ingest_file(file_path)
            
            st.session_state.ingestion_complete = True
            st.success("✅ Files processed and indexed!")

            # Show persistence status
            chunk_count = len(st.session_state.ingestion_system.metadata_store.chunks)
            vector_count = st.session_state.ingestion_system.vector_store.index.ntotal
            st.info(f"📊 Stored: {chunk_count} chunks, {vector_count} vectors")
            
        except Exception as e:
            st.error(f"❌ Ingestion failed: {str(e)}")

# Preview uploaded files
with st.expander("📄 Uploaded Files Preview"):
    show_uploaded_files()

# Query interface
if st.session_state.ingestion_complete:
    query = query_input()
    
    if query:
        with st.spinner("🔍 Searching and generating response..."):
            try:
                ingest = st.session_state.ingestion_system
                chunk_data = ingest.metadata_store.get_all_chunks()
                
                if not chunk_data:
                    st.warning("No indexed content found. Please ingest files first.")
                else:
                    # Perform retrieval
                    retrieved_chunks = biomedical_retrieval(
                        query=query,
                        vector_store=ingest.vector_store,
                        chunk_data=chunk_data,
                        k=10
                    )
                    
                    # Rerank and generate response
                    if retrieved_chunks:
                        top_passages = batch_rerank(query, retrieved_chunks)
                        best_chunks = [x["passage"] for x in top_passages]
                        
                        response = generate_cited_response(query, best_chunks)
                        st.markdown(response)
                    else:
                        st.warning("No relevant content found for your query.")
                    
            except Exception as e:
                st.error(f"Query processing failed: {str(e)}")
else:
    st.info("👆 Please upload and ingest files first to start querying.")

import streamlit as st

def query_input():
    """Query input interface"""
    st.subheader("🤖 Ask Questions")
    
    query = st.text_area(
        "Enter your query:",
        placeholder="e.g., What are the side effects of CAR-T cell therapy?",
        height=100
    )
    
    if st.button("🔍 Search", type="primary"):
        if query.strip():
            return query.strip()
        else:
            st.warning("Please enter a question")
    
    return None

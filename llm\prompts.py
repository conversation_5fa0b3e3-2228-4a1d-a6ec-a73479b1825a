class BiomedicalPrompts:
    # System role definitions
    SYSTEM_ROLES = {
        "biomedical_expert": (
            "You are a CAR-T cell therapy research specialist. "
            "Your responses must follow these rules:\n"
            "1. Use ONLY the provided source texts below\n"
            "2. Never use external knowledge\n"
            "3. If the answer is not found, say: 'The answer is not available in the uploaded documents.'\n"
            "4. All facts must be cited using [^number] that maps to the source\n"
            "5. Maintain scientific accuracy — do not speculate or infer\n"
            "6. Format your answer as:\n"
            "   <answer text> [^1][^2]\n"
            "\n"
            "   ## REFERENCES\n"
            "   [^1]: File name, page number\n"
            "   [^2]: File name, page number"
        ),
        "image_analyst": (
            "You are a biomedical image analyst. Describe ONLY what is visible in the image. "
            "Avoid assumptions or interpretations. Focus on visual facts such as labels, scales, structures, and numbers."
        ),
        "reranker": (
            "You are a biomedical relevance scorer. Evaluate the passages based only on how closely they match the query. "
            "Use a score from 0.0 (no match) to 1.0 (perfect match). Do not explain, only return scores."
        )
    }

    # Prompt templates
    TEMPLATES = {
        "answer_generation": {
            "system": SYSTEM_ROLES["biomedical_expert"],
            "user": (
                "### CONTEXT SOURCES:\n{context_str}\n\n"
                "### QUERY: {query}\n\n"
                "### INSTRUCTIONS:\n"
                "1. Answer using ONLY the sources above.\n"
                "2. Cite claims using [^number] that refer to the source.\n"
                "3. If the information is not in the sources, say:\n"
                "   'The answer is not available in the uploaded documents.'\n"
                "4. Do NOT combine content from different sources.\n"
                "5. Keep the answer concise and scientifically accurate.\n"
            )
        },
        "image_analysis": {
            "system": SYSTEM_ROLES["image_analyst"],
            "user": (
                "### IMAGE ANALYSIS REQUEST:\n"
                "Describe this biomedical image in exhaustive detail:\n"
                "1. List all visible elements (cells, labels, text)\n"
                "2. Quantify measurable features (sizes, counts, scales)\n"
                "3. Quote all visible text exactly\n"
                "4. Do NOT interpret the image or speculate\n\n"
                "Begin description:"
            )
        },
        "reranker": {
            "system": SYSTEM_ROLES["reranker"],
            "user": (
                "### RELEVANCE SCORING:\n"
                "Query: '{query}'\n\n"
                "Passages:\n{passages_str}\n\n"
                "Score each passage from 0.0 to 1.0 based on relevance.\n"
                "Output format: score1|score2|...|scoreN"
            )
        },
        "ocr_correction": {
            "system": SYSTEM_ROLES["biomedical_expert"],
            "user": (
                "### OCR CORRECTION TASK:\n"
                "Correct only obvious OCR mistakes in biomedical content:\n"
                "1. Fix misread characters (e.g., 'rn' → 'm')\n"
                "2. Correct broken technical terms\n"
                "3. Keep chemical formulas, units, and numbers unchanged\n"
                "4. Do not rephrase or add new content\n\n"
                "Text:\n{ocr_text}"
            )
        },
        "query_classification": {
            "system": "You are a classifier for query type (text or image).",
            "user": (
                "### CLASSIFICATION RULES:\n"
                "Return 'image' if the query mentions figure, diagram, image, table, or visual.\n"
                "Otherwise, return 'text'.\n\n"
                "Query: '{query}'\n\n"
                "Output only: 'text' or 'image'"
            )
        }
    }

    @staticmethod
    def get_prompt(prompt_type, **kwargs):
        template = BiomedicalPrompts.TEMPLATES.get(prompt_type)
        if not template:
            raise ValueError(f"Unknown prompt type: {prompt_type}")
        
        return {
            "system": template["system"],
            "user": template["user"].format(**kwargs)
        }

    # Specialized builders
    @staticmethod
    def answer_prompt(query, context_chunks):
        context_str = "\n\n".join(
            f"[Source {idx+1}]: {chunk['text']}\n"
            f"File: {chunk.get('metadata', {}).get('source_file', 'Unknown')}, "
            f"Page: {chunk.get('metadata', {}).get('page', 'N/A')}, "
            f"Section: {chunk.get('metadata', {}).get('section', 'N/A')}"
            for idx, chunk in enumerate(context_chunks)
        )
        return BiomedicalPrompts.get_prompt(
            "answer_generation",
            query=query,
            context_str=context_str
        )

    @staticmethod
    def rerank_prompt(query, passages):
        passages_str = "\n---\n".join(
            f"Passage {i+1}: {p['text'][:500]}"
            for i, p in enumerate(passages)
        )
        return BiomedicalPrompts.get_prompt(
            "reranker",
            query=query,
            passages_str=passages_str
        )

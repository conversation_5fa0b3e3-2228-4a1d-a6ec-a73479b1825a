import os
import fitz
import pandas as pd
from PIL import Image
import pytesseract
import openai
import logging
import re
from typing import List, Dict, Tuple, Any
from app.config import (
    OPENAI_MODEL, IMAGE_ANALYSIS_PROMPT, TABLE_EXTRACTION_SETTINGS,
    TEXT_CLEANING_PATTERNS
)

logger = logging.getLogger(__name__)
openai.api_key = os.getenv("OPENAI_API_KEY")

def extract_content(file_path):
    ext = os.path.splitext(file_path)[1].lower()

    try:
        if ext in ['.png', '.jpg', '.jpeg', '.tiff']:
            return handle_image(file_path)
        elif ext == '.pdf':
            return parse_pdf(file_path)
        elif ext in ['.docx', '.doc']:
            return extract_docx(file_path)
        elif ext == '.csv':
            return [(None, pd.read_csv(file_path).to_markdown(index=False))]
        elif ext == '.txt':
            with open(file_path, 'r', encoding='utf-8') as f:
                return [(None, f.read())]
        else:
            raise ValueError(f"Unsupported file type: {ext}")

    except Exception as e:
        logger.error(f" Content extraction failed for {file_path}: {e}")
        return []

# ======================== PDF ========================
def parse_pdf(pdf_path):
    """Enhanced PDF parsing with table extraction and text cleaning"""
    try:
        doc = fitz.open(pdf_path)
        extracted_content = []

        for page_num, page in enumerate(doc):
            page_content = extract_page_content(page, page_num + 1)
            if page_content:
                extracted_content.append((page_num + 1, page_content))

        doc.close()
        return extracted_content
    except Exception as e:
        logger.error(f"PDF parsing failed: {e}")
        return []

def extract_page_content(page, page_num: int) -> str:
    """Extract and combine text and tables from a PDF page"""
    try:
        # Extract regular text
        text_content = page.get_text("text")

        # Extract tables
        tables = extract_tables_from_page(page, page_num)

        # Extract images and figures (basic detection)
        images = extract_images_from_page(page, page_num)

        # Combine all content
        combined_content = []

        # Add cleaned text
        if text_content.strip():
            cleaned_text = clean_and_normalize_text(text_content)
            combined_content.append(cleaned_text)

        # Add tables with context
        for table_data in tables:
            combined_content.append(table_data)

        # Add image descriptions
        for image_desc in images:
            combined_content.append(image_desc)

        return "\n\n".join(combined_content)

    except Exception as e:
        logger.error(f"Page content extraction failed for page {page_num}: {e}")
        return ""

def extract_tables_from_page(page, page_num: int) -> List[str]:
    """Extract tables from PDF page using PyMuPDF table detection"""
    tables_content = []

    try:
        # Find tables using PyMuPDF's table detection with configured settings
        tables = page.find_tables(
            vertical_strategy=TABLE_EXTRACTION_SETTINGS["vertical_strategy"],
            horizontal_strategy=TABLE_EXTRACTION_SETTINGS["horizontal_strategy"]
        )

        for table_idx, table in enumerate(tables):
            try:
                # Extract table data
                table_data = table.extract()

                if table_data and len(table_data) > 1:  # Must have header + data
                    # Convert to markdown format
                    markdown_table = convert_table_to_markdown(table_data, page_num, table_idx + 1)
                    if markdown_table:
                        tables_content.append(markdown_table)

            except Exception as e:
                logger.warning(f"Failed to extract table {table_idx + 1} from page {page_num}: {e}")
                continue

    except Exception as e:
        logger.warning(f"Table detection failed for page {page_num}: {e}")

    return tables_content

def convert_table_to_markdown(table_data: List[List[str]], page_num: int, table_num: int) -> str:
    """Convert table data to structured markdown with metadata"""
    try:
        if not table_data or len(table_data) < 2:
            return ""

        # Clean and filter table data
        cleaned_data = []
        for row in table_data:
            cleaned_row = []
            for cell in row:
                if cell is None:
                    cleaned_row.append("")
                else:
                    # Clean cell content
                    cleaned_cell = clean_and_normalize_text(str(cell).strip())
                    cleaned_row.append(cleaned_cell)

            # Only add rows that have some content
            if any(cell.strip() for cell in cleaned_row):
                cleaned_data.append(cleaned_row)

        if len(cleaned_data) < 2:
            return ""

        # Create markdown table
        markdown_lines = []
        markdown_lines.append(f"\n**TABLE {table_num} (Page {page_num})**\n")

        # Header row
        header = cleaned_data[0]
        markdown_lines.append("| " + " | ".join(header) + " |")
        markdown_lines.append("| " + " | ".join(["---"] * len(header)) + " |")

        # Data rows
        for row in cleaned_data[1:]:
            # Pad row to match header length
            while len(row) < len(header):
                row.append("")
            markdown_lines.append("| " + " | ".join(row[:len(header)]) + " |")

        return "\n".join(markdown_lines) + "\n"

    except Exception as e:
        logger.error(f"Table markdown conversion failed: {e}")
        return ""

def extract_images_from_page(page, page_num: int) -> List[str]:
    """Extract and describe images/figures from PDF page"""
    image_descriptions = []

    try:
        # Get image list from page
        image_list = page.get_images()

        for img_idx, img in enumerate(image_list):
            try:
                # Extract image
                xref = img[0]
                pix = fitz.Pixmap(page.parent, xref)

                if pix.n - pix.alpha < 4:  # GRAY or RGB
                    # Create basic description
                    description = f"\n**FIGURE {img_idx + 1} (Page {page_num})**\n"
                    description += f"Image detected on page {page_num}. "
                    description += f"Dimensions: {pix.width}x{pix.height} pixels.\n"

                    image_descriptions.append(description)

                pix = None  # Free memory

            except Exception as e:
                logger.warning(f"Failed to process image {img_idx + 1} on page {page_num}: {e}")
                continue

    except Exception as e:
        logger.warning(f"Image extraction failed for page {page_num}: {e}")

    return image_descriptions

def clean_and_normalize_text(text: str) -> str:
    """Enhanced text cleaning and normalization"""
    if not text or not text.strip():
        return ""

    try:
        # Remove common OCR artifacts and normalize whitespace
        cleaned = text

        # Fix encoding issues
        cleaned = cleaned.encode('utf-8', errors='ignore').decode('utf-8')

        # Normalize whitespace
        cleaned = re.sub(TEXT_CLEANING_PATTERNS["multiple_spaces"], ' ', cleaned)
        cleaned = re.sub(TEXT_CLEANING_PATTERNS["multiple_newlines"], '\n\n', cleaned)

        # Remove standalone page numbers
        lines = cleaned.split('\n')
        filtered_lines = []
        for line in lines:
            line = line.strip()
            if line and not re.match(TEXT_CLEANING_PATTERNS["page_numbers"], line):
                # Remove bullet points but keep content
                line = re.sub(TEXT_CLEANING_PATTERNS["bullet_points"], '', line)
                if line.strip():
                    filtered_lines.append(line)

        cleaned = '\n'.join(filtered_lines)

        # Final cleanup
        cleaned = cleaned.strip()

        return cleaned

    except Exception as e:
        logger.warning(f"Text cleaning failed: {e}")
        return text.strip()

# ======================== DOCX ========================
def extract_docx(docx_path):
    try:
        from docx import Document
        doc = Document(docx_path)
        content = "\n".join(p.text for p in doc.paragraphs if p.text.strip())
        return [(None, content)]
    except Exception as e:
        logger.error(f"DOCX extraction failed: {e}")
        return []

# ======================== Image ========================
def handle_image(image_path):
    try:
        img = Image.open(image_path)

        if is_scanned(img):
            ocr_text = pytesseract.image_to_string(img)
            return [(1, refine_ocr_text(ocr_text))]
        else:
            vision_output = gpt4o_vision(image_path)
            return [(1, vision_output)]
    except Exception as e:
        logger.error(f"Image extraction failed: {e}")
        return []

def is_scanned(img):
    return img.mode == '1' or img.info.get('dpi', (0, 0))[0] > 300

# ======================== GPT-4o Vision ========================
def gpt4o_vision(image_path, prompt=BIOMEDICAL_IMAGE_PROMPT):
    try:
        with open(image_path, "rb") as img_file:
            response = openai.chat.completions.create(
                model=OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are a biomedical image analyst"},
                    {"role": "user", "content": prompt}
                ],
                files=[("image", img_file.read())],
                max_tokens=300
            )
        return response.choices[0].message.content.strip()
    except Exception as e:
        logger.error(f"Vision model failed: {e}")
        return "Image analysis failed."

# ======================== Enhanced OCR Refinement ========================
def refine_ocr_text(text):
    """Enhanced OCR text refinement with biomedical term preservation"""
    if not text.strip():
        return text

    try:
        # First apply basic cleaning
        cleaned_text = clean_and_normalize_text(text)

        # Then apply LLM-based OCR correction
        from llm.prompts import DocumentPrompts

        prompt = DocumentPrompts.get_prompt("ocr_correction", ocr_text=cleaned_text)

        response = openai.chat.completions.create(
            model=OPENAI_MODEL,
            messages=[
                {"role": "system", "content": prompt["system"]},
                {"role": "user", "content": prompt["user"]}
            ],
            temperature=0,
            max_tokens=min(len(cleaned_text) + 200, 2000)
        )

        corrected_text = response.choices[0].message.content.strip()

        # Final cleaning pass
        return clean_and_normalize_text(corrected_text)

    except Exception as e:
        logger.error(f"OCR refinement failed: {e}")
        # Return basic cleaned text as fallback
        return clean_and_normalize_text(text)

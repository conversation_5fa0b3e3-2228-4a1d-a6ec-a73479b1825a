import os
import fitz
import pandas as pd
from PIL import Image
import pytesseract
import openai
import logging
from app.config import OPENAI_MODEL, BIOMEDICAL_IMAGE_PROMPT

logger = logging.getLogger(__name__)
openai.api_key = os.getenv("OPENAI_API_KEY")

def extract_content(file_path):
    ext = os.path.splitext(file_path)[1].lower()

    try:
        if ext in ['.png', '.jpg', '.jpeg', '.tiff']:
            return handle_image(file_path)
        elif ext == '.pdf':
            return parse_pdf(file_path)
        elif ext in ['.docx', '.doc']:
            return extract_docx(file_path)
        elif ext == '.csv':
            return [(None, pd.read_csv(file_path).to_markdown(index=False))]
        elif ext == '.txt':
            with open(file_path, 'r', encoding='utf-8') as f:
                return [(None, f.read())]
        else:
            raise ValueError(f"Unsupported file type: {ext}")

    except Exception as e:
        logger.error(f" Content extraction failed for {file_path}: {e}")
        return []

# ======================== PDF ========================
def parse_pdf(pdf_path):
    try:
        doc = fitz.open(pdf_path)
        return [(i + 1, page.get_text("text")) for i, page in enumerate(doc)]
    except Exception as e:
        logger.error(f"PDF parsing failed: {e}")
        return []

# ======================== DOCX ========================
def extract_docx(docx_path):
    try:
        from docx import Document
        doc = Document(docx_path)
        content = "\n".join(p.text for p in doc.paragraphs if p.text.strip())
        return [(None, content)]
    except Exception as e:
        logger.error(f"DOCX extraction failed: {e}")
        return []

# ======================== Image ========================
def handle_image(image_path):
    try:
        img = Image.open(image_path)

        if is_scanned(img):
            ocr_text = pytesseract.image_to_string(img)
            return [(1, refine_ocr_text(ocr_text))]
        else:
            vision_output = gpt4o_vision(image_path)
            return [(1, vision_output)]
    except Exception as e:
        logger.error(f"Image extraction failed: {e}")
        return []

def is_scanned(img):
    return img.mode == '1' or img.info.get('dpi', (0, 0))[0] > 300

# ======================== GPT-4o Vision ========================
def gpt4o_vision(image_path, prompt=BIOMEDICAL_IMAGE_PROMPT):
    try:
        with open(image_path, "rb") as img_file:
            response = openai.chat.completions.create(
                model=OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are a biomedical image analyst"},
                    {"role": "user", "content": prompt}
                ],
                files=[("image", img_file.read())],
                max_tokens=300
            )
        return response.choices[0].message.content.strip()
    except Exception as e:
        logger.error(f"Vision model failed: {e}")
        return "Image analysis failed."

# ======================== OCR Refinement ========================
def refine_ocr_text(text):
    if not text.strip():
        return text

    try:
        response = openai.chat.completions.create(
            model=OPENAI_MODEL,
            messages=[
                {"role": "system", "content": "Correct OCR errors preserving biomedical terms"},
                {"role": "user", "content": text}
            ],
            max_tokens=len(text) + 100
        )
        return response.choices[0].message.content
    except Exception as e:
        logger.error(f"OCR refinement failed: {e}")
        return text
